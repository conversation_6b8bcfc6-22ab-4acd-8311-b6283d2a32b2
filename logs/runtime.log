[09-04 14:30:33.915] [info] =====算法库初始化开始=====
[09-04 14:30:33.915] [info] Initializing unified resource manager...
[09-04 14:30:33.916] [info] Initializing GPU resource manager...
[09-04 14:30:33.992] [info] GPU resource manager initialized successfully
[09-04 14:30:33.992] [info] Initializing memory pool manager...
[09-04 14:30:33.993] [info] CUDA memory pool created with max size: 512MB
[09-04 14:30:33.993] [info] Host memory pool created with max size: 1024MB
[09-04 14:30:33.993] [info] Memory pool manager initialized successfully
[09-04 14:30:33.993] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-04 14:30:33.993] [info] Initializing FFT resource pools for 5 segment lengths
[09-04 14:30:33.994] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-04 14:30:33.995] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-04 14:30:33.995] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-04 14:30:33.995] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-04 14:30:33.995] [info] All FFT resource pools initialized successfully
[09-04 14:30:33.995] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-04 14:30:33.995] [info] Unified resource manager initialized successfully
[09-04 14:30:33.995] [info] Loading TensorRT engine from: data/trt_net_fp16_v1.trt
[09-04 14:30:34.008] [info] TensorRT engine loaded successfully: data/trt_net_fp16_v1.trt
[09-04 14:30:34.008] [info] Engine info: 2 bindings
[09-04 14:30:34.008] [info]   Binding 0: input [1x1x1024x1024x2]
[09-04 14:30:34.008] [info]   Binding 1: output [1x1x512x1024]
[09-04 14:30:34.008] [info] 模型输入维度: [1x1024x1024x2]
[09-04 14:30:34.008] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-04 14:30:34.013] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-04 14:30:34.013] [info] - 显存 - 算法初始化完成: 使用 604.5 MB
[09-04 14:30:34.013] [info] - 内存 - 算法初始化完成: 使用 257.4 MB
[09-04 14:30:34.013] [info] =====算法库初始化完成=====
[09-04 14:30:34.102] [info] =====开始执行轻量化目标检测=====
[09-04 14:30:34.183] [info] 检测到 7 个目标中心点
[09-04 14:30:34.183] [info] 开始提取列数据段，共 7 个中心点
[09-04 14:30:34.183] [info] 检测点[0]: 列=1847, 行=254, 段长度=512
[09-04 14:30:34.183] [info] 检测点[1]: 列=1968, 行=254, 段长度=512
[09-04 14:30:34.183] [info] 检测点[2]: 列=1741, 行=464, 段长度=512
[09-04 14:30:34.184] [info] 检测点[3]: 列=1786, 行=464, 段长度=512
[09-04 14:30:34.184] [info] 检测点[4]: 列=1866, 行=465, 段长度=512
[09-04 14:30:34.184] [info] 检测点[5]: 列=1870, 行=467, 段长度=512
[09-04 14:30:34.184] [info] 检测点[6]: 列=1842, 行=988, 段长度=64
[09-04 14:30:34.184] [info] 目标检测耗时(ms): 预处理:11 推理:63 后处理:6 提取:0 总:80 (FPS:12.50)
[09-04 14:31:09.994] [info] =====开始目标跟踪======
[09-04 14:31:09.996] [info] FFT处理完成[0]: 列=1847, 行=254, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[1]: 列=1968, 行=254, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[2]: 列=1741, 行=464, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[3]: 列=1786, 行=464, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[4]: 列=1866, 行=465, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[5]: 列=1870, 行=467, 段长度=512
[09-04 14:31:09.996] [info] FFT处理完成[6]: 列=1842, 行=988, 段长度=64
[09-04 14:51:59.454] [info] =====算法库初始化开始=====
[09-04 14:51:59.454] [info] Initializing unified resource manager...
[09-04 14:51:59.455] [info] Initializing GPU resource manager...
[09-04 14:51:59.528] [info] GPU resource manager initialized successfully
[09-04 14:51:59.528] [info] Initializing memory pool manager...
[09-04 14:51:59.528] [info] CUDA memory pool created with max size: 512MB
[09-04 14:51:59.528] [info] Host memory pool created with max size: 1024MB
[09-04 14:51:59.528] [info] Memory pool manager initialized successfully
[09-04 14:51:59.528] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-04 14:51:59.528] [info] Initializing FFT resource pools for 5 segment lengths
[09-04 14:51:59.530] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-04 14:51:59.530] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-04 14:51:59.530] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-04 14:51:59.531] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-04 14:51:59.531] [info] All FFT resource pools initialized successfully
[09-04 14:51:59.531] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-04 14:51:59.531] [info] Unified resource manager initialized successfully
[09-04 14:51:59.531] [info] Loading TensorRT engine from: data/trt_net_fp16_v1.trt
[09-04 14:51:59.543] [info] TensorRT engine loaded successfully: data/trt_net_fp16_v1.trt
[09-04 14:51:59.543] [info] Engine info: 2 bindings
[09-04 14:51:59.543] [info]   Binding 0: input [1x1x1024x1024x2]
[09-04 14:51:59.543] [info]   Binding 1: output [1x1x512x1024]
[09-04 14:51:59.543] [info] 模型输入维度: [1x1024x1024x2]
[09-04 14:51:59.543] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-04 14:51:59.548] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-04 14:51:59.548] [info] - 显存 - 算法初始化完成: 使用 717.2 MB
[09-04 14:51:59.548] [info] - 内存 - 算法初始化完成: 使用 258.5 MB
[09-04 14:51:59.548] [info] =====算法库初始化完成=====
[09-04 14:51:59.635] [info] =====开始执行轻量化目标检测=====
[09-04 14:51:59.711] [info] 检测到 7 个目标中心点
[09-04 14:51:59.711] [info] 开始提取列数据段，共 7 个中心点
[09-04 14:51:59.711] [info] 检测点[0]: 列=1847, 行=254, 段长度=512
[09-04 14:51:59.711] [info] 检测点[1]: 列=1968, 行=254, 段长度=512
[09-04 14:51:59.712] [info] 检测点[2]: 列=1741, 行=464, 段长度=512
[09-04 14:51:59.712] [info] 检测点[3]: 列=1786, 行=464, 段长度=512
[09-04 14:51:59.712] [info] 检测点[4]: 列=1866, 行=465, 段长度=512
[09-04 14:51:59.712] [info] 检测点[5]: 列=1870, 行=467, 段长度=512
[09-04 14:51:59.712] [info] 检测点[6]: 列=1842, 行=988, 段长度=64
[09-04 14:51:59.712] [info] 目标检测耗时(ms): 预处理:14 推理:56 后处理:5 提取:0 总:75 (FPS:13.33)
[09-04 14:52:04.641] [info] =====开始目标跟踪======
[09-04 14:52:04.641] [info] FFT处理完成[0]: 列=1847, 行=254, 段长度=512
[09-04 14:52:04.641] [info] FFT处理完成[1]: 列=1968, 行=254, 段长度=512
[09-04 14:52:04.641] [info] FFT处理完成[2]: 列=1741, 行=464, 段长度=512
[09-04 14:52:04.641] [info] FFT处理完成[3]: 列=1786, 行=464, 段长度=512
[09-04 14:52:04.641] [info] FFT处理完成[4]: 列=1866, 行=465, 段长度=512
[09-04 14:52:04.642] [info] FFT处理完成[5]: 列=1870, 行=467, 段长度=512
[09-04 14:52:04.642] [info] FFT处理完成[6]: 列=1842, 行=988, 段长度=64
