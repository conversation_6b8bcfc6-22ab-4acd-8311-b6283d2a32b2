#include "logger.hpp"
// #include "config.hpp"
#include <iostream>
#include <fstream>
#include <string>

// #include <filesystem>

void initLogger(const std::string& log_path) {
    try {

        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        // 日志文件轮换: 文件名, 最大文件大小(5MB), 保留文件个数(3个)
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(log_path, 10 * 1024 * 1024, 3);

        console_sink->set_level(spdlog::level::err);
        file_sink->set_level(spdlog::level::info);

        auto logger = std::make_shared<spdlog::logger>("multi_sink",
                            spdlog::sinks_init_list{console_sink, file_sink});
        logger->set_level(spdlog::level::info);
        logger->set_pattern("[%m-%d %H:%M:%S.%e] [%^%l%$] %v");

        spdlog::set_default_logger(logger);
        spdlog::flush_on(spdlog::level::info);

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
    }
}

// 使用默认配置的日志初始化版本
void initLogger() {
    try {
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::info);

        auto logger = std::make_shared<spdlog::logger>("console_logger", console_sink);
        logger->set_level(spdlog::level::info);
        logger->set_pattern("[%Y-%m-%d %H:%M:%S] [%^%l%$] %v");

        spdlog::set_default_logger(logger);
        spdlog::flush_on(spdlog::level::info);

        spdlog::info("日志系统初始化完成（控制台模式）");

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "日志初始化失败: " << ex.what() << std::endl;
    }
}

// 直接从配置文件初始化日志系统
bool initLoggerFromConfig(const std::string& config_path) {
    try {
        // 简单的JSON解析来获取日志路径
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            std::cerr << "无法打开配置文件: " << config_path << std::endl;
            return false;
        }

        std::string line;
        std::string log_path;
        bool found_log_path = false;

        // 简单的文本搜索方式查找日志路径
        while (std::getline(config_file, line)) {
            if (line.find("\"log_file_path\"") != std::string::npos) {
                // 提取路径值
                size_t start = line.find(":") + 1;
                size_t first_quote = line.find("\"", start);
                size_t second_quote = line.find("\"", first_quote + 1);

                if (first_quote != std::string::npos && second_quote != std::string::npos) {
                    log_path = line.substr(first_quote + 1, second_quote - first_quote - 1);
                    found_log_path = true;
                    break;
                }
            }
        }

        if (!found_log_path || log_path.empty()) {
            std::cerr << "配置文件中未找到有效的日志路径，使用控制台模式" << std::endl;
            initLogger();  // 回退到控制台模式
            return true;
        }

        // 使用找到的路径初始化日志
        initLogger(log_path);
        return true;

    } catch (const std::exception& e) {
        std::cerr << "从配置文件初始化日志失败: " << e.what() << std::endl;
        initLogger();  // 回退到控制台模式
        return true;
    }
}
